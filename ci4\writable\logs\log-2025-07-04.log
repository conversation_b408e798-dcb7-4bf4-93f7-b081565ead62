DEBUG - 2025-07-04 15:49:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 15:50:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-07-04 15:50:42 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "lab11_ci(1)"
[Method: GET, Route: public/lab11_ci%281%29]
in SYSTEMPATH\Router\Router.php on line 739.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('public/lab11_ci(1)')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('public/lab11_ci(1)')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php')
CRITICAL - 2025-07-04 15:51:00 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "lab11_ci(1)"
[Method: GET, Route: public/lab11_ci%281%29/ci4/index.php]
in SYSTEMPATH\Router\Router.php on line 739.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('public/lab11_ci(1)/ci4/index.php')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('public/lab11_ci(1)/ci4/index.php')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php')
DEBUG - 2025-07-04 16:52:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 17:05:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 18:47:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 18:47:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 19:06:31 --> POST request received
DEBUG - 2025-07-04 19:06:31 --> Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryh5HScx6P0wPkbSTU
DEBUG - 2025-07-04 19:06:31 --> POST data: {"judul":"dasdasdasd","isi":"asdasdasd","status":"1"}
DEBUG - 2025-07-04 19:06:31 --> Form data: {"judul":"dasdasdasd","isi":"asdasdasd","slug":"dasdasdasd","status":"1","created_at":"2025-07-04 19:06:31","updated_at":"2025-07-04 19:06:31"}
DEBUG - 2025-07-04 19:07:02 --> POST request received
DEBUG - 2025-07-04 19:07:02 --> Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryZiBFb6M0Uvno8QKa
DEBUG - 2025-07-04 19:07:02 --> POST data: {"judul":"artikel sample","isi":"asdasdas","status":"1"}
DEBUG - 2025-07-04 19:07:02 --> Form data: {"judul":"artikel sample","isi":"asdasdas","slug":"artikel-sample","status":"1","created_at":"2025-07-04 19:07:02","updated_at":"2025-07-04 19:07:02"}
DEBUG - 2025-07-04 20:03:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 20:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 20:03:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 20:05:22 --> POST request received
DEBUG - 2025-07-04 20:05:22 --> Content-Type: multipart/form-data; boundary=----WebKitFormBoundarysRKOd9vrV7a43iCE
DEBUG - 2025-07-04 20:05:22 --> POST data: {"judul":"de gea masuk fiorentina setelah 1 musim free agent","isi":"contoh artikel","status":"1"}
DEBUG - 2025-07-04 20:05:22 --> Form data: {"judul":"de gea masuk fiorentina setelah 1 musim free agent","isi":"contoh artikel","slug":"de-gea-masuk-fiorentina-setelah-1-musim-free-agent","status":"1","created_at":"2025-07-04 20:05:22","updated_at":"2025-07-04 20:05:22"}
DEBUG - 2025-07-04 20:07:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 20:07:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-04 20:07:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
