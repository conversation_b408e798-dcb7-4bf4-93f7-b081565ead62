/* Article Detail Container */
.article-detail-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 2rem;
    margin-bottom: 2rem;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: var(--secondary-color);
}

.separator {
    color: #d1d5db;
}

.current {
    color: var(--dark-color);
    font-weight: 500;
}

/* Article Header */
.article-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f3f4f6;
}

.article-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.2;
    margin: 1rem 0;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.9rem;
}

.meta-item i {
    color: var(--primary-color);
}

/* Article Image */
.article-image-container {
    margin: 2rem 0;
    text-align: center;
}

.article-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.image-caption {
    margin-top: 1rem;
    font-style: italic;
    color: #6b7280;
    font-size: 0.9rem;
}

/* Article Content */
.article-content {
    margin-top: 2rem;
}

.content-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #374151;
    margin-bottom: 3rem;
}

.content-body p {
    margin-bottom: 1.5rem;
}

/* Article Actions */
.article-actions {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.action-btn {
    background: white;
    border: 2px solid #e5e7eb;
    color: #6b7280;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2);
}

.action-btn.liked {
    border-color: #ef4444;
    color: #ef4444;
}

/* Social Share */
.social-share {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-label {
    font-weight: 500;
    color: var(--dark-color);
}

.social-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-btn:hover {
    transform: scale(1.1);
}

.social-btn.facebook { background: #1877f2; }
.social-btn.twitter { background: #1da1f2; }
.social-btn.whatsapp { background: #25d366; }
.social-btn.telegram { background: #0088cc; }

/* Article Navigation */
.article-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 3rem 0;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
    flex-wrap: wrap;
    gap: 1rem;
}

.nav-btn {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

.nav-actions {
    display: flex;
    gap: 1rem;
}

/* Related Articles */
.related-articles {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #f3f4f6;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: var(--primary-color);
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-item {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.related-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.related-image {
    width: 60px;
    height: 60px;
    background: #e5e7eb;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: #9ca3af;
}

.related-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.related-content p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.related-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.related-link:hover {
    color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .article-detail-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .article-title {
        font-size: 2rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 1rem;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }

    .article-navigation {
        flex-direction: column;
        text-align: center;
    }

    .nav-actions {
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .social-share {
        flex-direction: column;
        gap: 1rem;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }
}

/* Print Styles */
@media print {
    .article-actions,
    .article-navigation,
    .related-articles {
        display: none;
    }

    .article-detail-container {
        box-shadow: none;
        margin: 0;
        padding: 1rem;
    }
}
